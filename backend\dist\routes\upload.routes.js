"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const upload_controller_1 = require("../controllers/upload.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const upload_middleware_1 = require("../middleware/upload.middleware");
const router = (0, express_1.Router)();
router.use(auth_middleware_1.requestId);
router.use(auth_middleware_1.recordIP);
router.post('/single', auth_middleware_1.authenticateToken, upload_middleware_1.checkUploadLimits, upload_middleware_1.uploadSingle, upload_middleware_1.handleUploadError, upload_middleware_1.validateUploadedFile, upload_controller_1.UploadController.uploadSingle);
router.post('/batch', (req, res, next) => {
    console.log('🚀 批量上传路由开始处理请求');
    console.log('Content-Type:', req.get('Content-Type'));
    console.log('Content-Length:', req.get('Content-Length'));
    next();
}, auth_middleware_1.authenticateToken, (0, auth_middleware_1.requireLevel)('vip1'), upload_middleware_1.checkUploadLimits, upload_middleware_1.uploadMultiple, upload_middleware_1.handleUploadError, upload_middleware_1.validateUploadedFiles, upload_controller_1.UploadController.uploadBatch);
router.get('/limits', auth_middleware_1.authenticateToken, upload_controller_1.UploadController.getUploadLimits);
router.get('/history', auth_middleware_1.authenticateToken, upload_controller_1.UploadController.getUploadHistory);
router.get('/stats', auth_middleware_1.authenticateToken, upload_controller_1.UploadController.getUploadStats);
router.delete('/:imageId', auth_middleware_1.authenticateToken, upload_controller_1.UploadController.deleteUpload);
router.post('/retry/:imageId', auth_middleware_1.authenticateToken, upload_controller_1.UploadController.retryUpload);
router.get('/providers', auth_middleware_1.authenticateToken, upload_controller_1.UploadController.getAvailableProviders);
exports.default = router;
//# sourceMappingURL=upload.routes.js.map