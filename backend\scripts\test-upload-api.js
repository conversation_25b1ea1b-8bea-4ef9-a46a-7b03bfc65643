/**
 * 测试上传 API 的脚本
 * 模拟前端发送文件上传请求
 */

const FormData = require('form-data');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

async function testUploadAPI() {
  try {
    console.log('🧪 开始测试上传 API...\n');

    // 1. 首先获取认证 token
    console.log('🔐 获取认证 token...');
    
    // 使用测试用户登录
    const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
      username: 'tcl_test_user',
      password: 'test_password_hash'
    }).catch(async (error) => {
      // 如果登录失败，可能是密码不对，尝试注册
      console.log('登录失败，尝试注册新用户...');
      
      const registerResponse = await axios.post('http://localhost:3000/api/auth/register', {
        username: 'tcl_test_user_2',
        email: '<EMAIL>',
        password: 'test123456'
      });
      
      console.log('✅ 注册成功:', registerResponse.data);
      
      // 注册后再次登录
      return await axios.post('http://localhost:3000/api/auth/login', {
        username: 'tcl_test_user_2',
        password: 'test123456'
      });
    });

    const token = loginResponse.data.data.token;
    console.log('✅ 获取 token 成功');

    // 2. 创建测试图片文件
    console.log('\n🖼️  创建测试图片...');
    
    const testImageBuffer = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );

    // 3. 准备 FormData
    const formData = new FormData();
    formData.append('file', testImageBuffer, {
      filename: 'test-upload.png',
      contentType: 'image/png'
    });

    // 可选：指定使用 TCL 接口
    formData.append('providerId', '4'); // TCL 接口的 ID

    console.log('📤 发送上传请求...');
    console.log('目标接口: http://localhost:3000/api/upload/single');

    // 4. 发送上传请求
    const uploadResponse = await axios.post(
      'http://localhost:3000/api/upload/single',
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${token}`
        },
        timeout: 30000
      }
    );

    console.log('\n📊 上传结果:');
    console.log('状态码:', uploadResponse.status);
    console.log('响应数据:', JSON.stringify(uploadResponse.data, null, 2));

    if (uploadResponse.data.success) {
      console.log('\n✅ 上传测试成功！');
      console.log('图片ID:', uploadResponse.data.data.imageId);
      console.log('系统URL:', uploadResponse.data.data.systemUrl);
      console.log('文件大小:', uploadResponse.data.data.fileSize);
      console.log('MIME类型:', uploadResponse.data.data.mimeType);
    } else {
      console.log('\n❌ 上传测试失败');
      console.log('错误信息:', uploadResponse.data.error);
    }

    // 5. 测试获取可用接口列表
    console.log('\n📋 获取可用接口列表...');
    
    const providersResponse = await axios.get(
      'http://localhost:3000/api/upload/providers',
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );

    console.log('可用接口:');
    providersResponse.data.data.forEach((provider, index) => {
      console.log(`  ${index + 1}. ${provider.name} (ID: ${provider.id})`);
      console.log(`     端点: ${provider.endpoint}`);
      console.log(`     等级要求: ${provider.requiredLevel}`);
      console.log(`     高级接口: ${provider.isPremium ? '是' : '否'}`);
      console.log('');
    });

  } catch (error) {
    console.error('\n💥 测试失败:', error.message);
    
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
    
    throw error;
  }
}

// 执行测试
if (require.main === module) {
  testUploadAPI()
    .then(() => {
      console.log('\n🎉 API 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 API 测试失败:', error.message);
      process.exit(1);
    });
}

module.exports = { testUploadAPI };
