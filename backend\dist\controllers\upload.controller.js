"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadController = void 0;
const database_1 = require("../config/database");
const types_1 = require("../types");
const file_service_1 = require("../services/file.service");
const image_processing_service_1 = require("../services/image-processing.service");
const user_provider_service_1 = require("../services/user-provider.service");
const queue_service_1 = require("../services/queue.service");
const crypto_1 = __importDefault(require("crypto"));
const path_1 = __importDefault(require("path"));
class UploadController {
    static async uploadSingle(req, res, next) {
        try {
            const userId = req.user?.id;
            const file = req.file;
            const { providerId } = req.body;
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            if (!file) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '文件不能为空',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const canUpload = await UploadController.checkUploadLimits(parseInt(userId), file.size);
            if (!canUpload.allowed) {
                res.status(403).json({
                    success: false,
                    error: {
                        code: canUpload.errorCode,
                        message: canUpload.message,
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            if (providerId) {
                const canUseProvider = await user_provider_service_1.UserProviderService.canUseProvider(parseInt(userId), parseInt(providerId));
                if (!canUseProvider) {
                    res.status(403).json({
                        success: false,
                        error: {
                            code: types_1.ErrorCodes.FORBIDDEN,
                            message: '您没有权限使用指定的接口',
                            timestamp: new Date().toISOString(),
                        }
                    });
                    return;
                }
            }
            const fileBuffer = file.buffer;
            const fileHash = crypto_1.default.createHash('sha256').update(fileBuffer).digest('hex');
            const fileMd5 = crypto_1.default.createHash('md5').update(fileBuffer).digest('hex');
            const existingImage = await database_1.prisma.image.findUnique({
                where: { fileHash },
                include: {
                    userImages: {
                        where: { userId: parseInt(userId) }
                    },
                    imageLinks: {
                        include: {
                            provider: true
                        }
                    }
                }
            });
            if (existingImage) {
                if (existingImage.userImages.length === 0) {
                    await database_1.prisma.userImage.create({
                        data: {
                            userId: parseInt(userId),
                            imageId: existingImage.id,
                            isOriginalUploader: false,
                        }
                    });
                }
                await UploadController.logUpload(parseInt(userId), existingImage.id, 'reuse', req);
                const response = {
                    success: true,
                    message: '文件已存在，直接返回链接',
                    data: {
                        imageId: existingImage.id.toString(),
                        systemUrl: existingImage.systemUrl,
                        isReused: true,
                        uploadTime: new Date().toISOString(),
                        fileSize: Number(existingImage.fileSize),
                        mimeType: existingImage.mimeType,
                    }
                };
                res.json(response);
                return;
            }
            const imageInfo = await image_processing_service_1.ImageProcessingService.getImageInfo(fileBuffer, file.mimetype);
            const fileName = `${fileHash}${path_1.default.extname(file.originalname)}`;
            const filePath = await file_service_1.FileService.saveFile(fileBuffer, fileName);
            const image = await database_1.prisma.image.create({
                data: {
                    originalName: file.originalname,
                    fileHash,
                    fileMd5,
                    fileSize: BigInt(file.size),
                    mimeType: file.mimetype,
                    width: imageInfo.width ?? null,
                    height: imageInfo.height ?? null,
                    systemUrl: `/api/images/${fileHash}`,
                    uploadStatus: 'processing',
                }
            });
            await database_1.prisma.userImage.create({
                data: {
                    userId: parseInt(userId),
                    imageId: image.id,
                    isOriginalUploader: true,
                }
            });
            await UploadController.logUpload(parseInt(userId), image.id, 'upload', req);
            const jobData = {
                imageId: image.id,
                userId: parseInt(userId),
                filePath,
                providerId: providerId ? parseInt(providerId) : undefined,
                fileInfo: {
                    originalName: file.originalname,
                    mimeType: file.mimetype,
                    size: file.size,
                    hash: fileHash,
                }
            };
            await queue_service_1.QueueService.addUploadJob(jobData);
            const response = {
                success: true,
                message: '文件上传成功',
                data: {
                    imageId: image.id.toString(),
                    systemUrl: image.systemUrl,
                    isReused: false,
                    uploadTime: new Date().toISOString(),
                    fileSize: file.size,
                    mimeType: file.mimetype,
                }
            };
            res.status(201).json(response);
        }
        catch (error) {
            console.error('上传文件错误:', error);
            next(error);
        }
    }
    static async uploadBatch(req, res, next) {
        try {
            const userId = req.user?.id;
            let files = [];
            if (req.files) {
                if (Array.isArray(req.files)) {
                    files = req.files;
                }
                else {
                    const fileFields = req.files;
                    files = [
                        ...(fileFields.files || []),
                        ...(fileFields.file || [])
                    ];
                }
            }
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            if (!files || files.length === 0) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '文件不能为空',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const results = [];
            const errors = [];
            for (const file of files) {
                try {
                    const canUpload = await UploadController.checkUploadLimits(parseInt(userId), file.size);
                    if (!canUpload.allowed) {
                        errors.push({
                            filename: file.originalname,
                            error: canUpload.message
                        });
                        continue;
                    }
                    const fileBuffer = file.buffer;
                    const fileHash = crypto_1.default.createHash('sha256').update(fileBuffer).digest('hex');
                    const existingImage = await database_1.prisma.image.findUnique({
                        where: { fileHash }
                    });
                    if (existingImage) {
                        results.push({
                            filename: file.originalname,
                            imageId: existingImage.id.toString(),
                            systemUrl: existingImage.systemUrl,
                            isReused: true
                        });
                    }
                    else {
                        const imageInfo = await image_processing_service_1.ImageProcessingService.getImageInfo(fileBuffer, file.mimetype);
                        const fileName = `${fileHash}${path_1.default.extname(file.originalname)}`;
                        const filePath = await file_service_1.FileService.saveFile(fileBuffer, fileName);
                        const image = await database_1.prisma.image.create({
                            data: {
                                originalName: file.originalname,
                                fileHash,
                                fileMd5: crypto_1.default.createHash('md5').update(fileBuffer).digest('hex'),
                                fileSize: BigInt(file.size),
                                mimeType: file.mimetype,
                                width: imageInfo.width ?? null,
                                height: imageInfo.height ?? null,
                                systemUrl: `/api/images/${fileHash}`,
                                uploadStatus: 'processing',
                            }
                        });
                        await database_1.prisma.userImage.create({
                            data: {
                                userId: parseInt(userId),
                                imageId: image.id,
                                isOriginalUploader: true,
                            }
                        });
                        results.push({
                            filename: file.originalname,
                            imageId: image.id.toString(),
                            systemUrl: image.systemUrl,
                            isReused: false
                        });
                        await queue_service_1.QueueService.addUploadJob({
                            imageId: image.id,
                            userId: parseInt(userId),
                            filePath,
                            fileInfo: {
                                originalName: file.originalname,
                                mimeType: file.mimetype,
                                size: file.size,
                                hash: fileHash,
                            }
                        });
                    }
                }
                catch (fileError) {
                    console.error(`处理文件 ${file.originalname} 时出错:`, fileError);
                    errors.push({
                        filename: file.originalname,
                        error: '文件处理失败'
                    });
                }
            }
            res.json({
                success: true,
                message: `批量上传完成，成功: ${results.length}，失败: ${errors.length}`,
                data: {
                    results,
                    errors,
                    summary: {
                        total: files.length,
                        success: results.length,
                        failed: errors.length
                    }
                }
            });
        }
        catch (error) {
            console.error('批量上传错误:', error);
            next(error);
        }
    }
    static async checkUploadLimits(userId, fileSize) {
        try {
            const user = await database_1.prisma.user.findUnique({
                where: { id: userId },
                select: { userLevel: true }
            });
            if (!user) {
                return {
                    allowed: false,
                    errorCode: 'USER_NOT_FOUND',
                    message: '用户不存在'
                };
            }
            const levelConfig = await database_1.prisma.userLevelConfig.findUnique({
                where: { level: user.userLevel }
            });
            if (!levelConfig) {
                return {
                    allowed: false,
                    errorCode: 'LEVEL_CONFIG_NOT_FOUND',
                    message: '用户等级配置不存在'
                };
            }
            if (fileSize > Number(levelConfig.maxFileSize)) {
                return {
                    allowed: false,
                    errorCode: types_1.ErrorCodes.FILE_TOO_LARGE,
                    message: `文件大小超过限制，最大允许 ${Math.round(Number(levelConfig.maxFileSize) / 1024 / 1024)}MB`
                };
            }
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            const todayUploads = await database_1.prisma.uploadLog.count({
                where: {
                    userId,
                    actionType: 'upload',
                    isSuccess: true,
                    createdAt: {
                        gte: today,
                        lt: tomorrow
                    }
                }
            });
            if (todayUploads >= levelConfig.maxDailyUploads) {
                return {
                    allowed: false,
                    errorCode: types_1.ErrorCodes.UPLOAD_LIMIT_EXCEEDED,
                    message: `今日上传次数已达上限 ${levelConfig.maxDailyUploads} 次`
                };
            }
            return { allowed: true };
        }
        catch (error) {
            console.error('检查上传限制时出错:', error);
            return {
                allowed: false,
                errorCode: types_1.ErrorCodes.INTERNAL_ERROR,
                message: '检查上传限制时出错'
            };
        }
    }
    static async logUpload(userId, imageId, actionType, req) {
        try {
            const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
            const userAgent = req.get('User-Agent') || '';
            await database_1.prisma.uploadLog.create({
                data: {
                    userId,
                    imageId,
                    actionType,
                    ipAddress: clientIP,
                    userAgent,
                    isSuccess: true,
                }
            });
        }
        catch (error) {
            console.error('记录上传日志失败:', error);
        }
    }
    static async getUploadLimits(req, res, next) {
        try {
            const userId = req.user?.id;
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const user = await database_1.prisma.user.findUnique({
                where: { id: parseInt(userId) },
                select: { userLevel: true }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: 'USER_NOT_FOUND',
                        message: '用户不存在',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const levelConfig = await database_1.prisma.userLevelConfig.findUnique({
                where: { level: user.userLevel }
            });
            if (!levelConfig) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: 'LEVEL_CONFIG_NOT_FOUND',
                        message: '用户等级配置不存在',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            const todayUploads = await database_1.prisma.uploadLog.count({
                where: {
                    userId: parseInt(userId),
                    actionType: 'upload',
                    isSuccess: true,
                    createdAt: {
                        gte: today,
                        lt: tomorrow
                    }
                }
            });
            res.json({
                success: true,
                data: {
                    userLevel: user.userLevel,
                    maxDailyUploads: levelConfig.maxDailyUploads,
                    maxFileSize: Number(levelConfig.maxFileSize),
                    maxStorageSpace: Number(levelConfig.maxStorageSpace),
                    todayUploads,
                    remainingUploads: Math.max(0, levelConfig.maxDailyUploads - todayUploads),
                    canChooseProvider: levelConfig.canChooseProvider,
                    visibleProviderCount: levelConfig.visibleProviderCount,
                }
            });
        }
        catch (error) {
            console.error('获取上传限制信息错误:', error);
            next(error);
        }
    }
    static async getUploadHistory(req, res, next) {
        try {
            const userId = req.user?.id;
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 20;
            const offset = (page - 1) * limit;
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const [uploads, total] = await Promise.all([
                database_1.prisma.userImage.findMany({
                    where: { userId: parseInt(userId) },
                    include: {
                        image: {
                            include: {
                                imageLinks: {
                                    include: {
                                        provider: true
                                    }
                                }
                            }
                        }
                    },
                    orderBy: { createdAt: 'desc' },
                    skip: offset,
                    take: limit,
                }),
                database_1.prisma.userImage.count({
                    where: { userId: parseInt(userId) }
                })
            ]);
            const uploadHistory = uploads.map(upload => ({
                id: upload.image.id,
                originalName: upload.image.originalName,
                fileSize: Number(upload.image.fileSize),
                mimeType: upload.image.mimeType,
                systemUrl: upload.image.systemUrl,
                uploadStatus: upload.image.uploadStatus,
                isOriginalUploader: upload.isOriginalUploader,
                accessCount: upload.accessCount,
                createdAt: upload.createdAt.toISOString(),
                links: upload.image.imageLinks.map(link => ({
                    provider: link.provider.name,
                    url: link.externalUrl,
                    status: link.status,
                }))
            }));
            res.json({
                success: true,
                data: {
                    uploads: uploadHistory,
                    pagination: {
                        page,
                        limit,
                        total,
                        totalPages: Math.ceil(total / limit),
                    }
                }
            });
        }
        catch (error) {
            console.error('获取上传历史错误:', error);
            next(error);
        }
    }
    static async getUploadStats(req, res, next) {
        try {
            const userId = req.user?.id;
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const [totalUploads, totalSize, todayUploads, thisMonthUploads] = await Promise.all([
                database_1.prisma.userImage.count({
                    where: { userId: parseInt(userId) }
                }),
                database_1.prisma.image.aggregate({
                    where: {
                        userImages: {
                            some: { userId: parseInt(userId) }
                        }
                    },
                    _sum: { fileSize: true }
                }),
                database_1.prisma.uploadLog.count({
                    where: {
                        userId: parseInt(userId),
                        actionType: 'upload',
                        isSuccess: true,
                        createdAt: {
                            gte: new Date(new Date().setHours(0, 0, 0, 0))
                        }
                    }
                }),
                database_1.prisma.uploadLog.count({
                    where: {
                        userId: parseInt(userId),
                        actionType: 'upload',
                        isSuccess: true,
                        createdAt: {
                            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
                        }
                    }
                })
            ]);
            res.json({
                success: true,
                data: {
                    totalUploads,
                    totalSize: Number(totalSize._sum?.fileSize || 0),
                    todayUploads,
                    thisMonthUploads,
                }
            });
        }
        catch (error) {
            console.error('获取上传统计错误:', error);
            next(error);
        }
    }
    static async deleteUpload(req, res, next) {
        try {
            const userId = req.user?.id;
            const imageId = parseInt(req.params.imageId);
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const userImage = await database_1.prisma.userImage.findFirst({
                where: {
                    userId: parseInt(userId),
                    imageId,
                },
                include: {
                    image: true
                }
            });
            if (!userImage) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: 'IMAGE_NOT_FOUND',
                        message: '图片不存在或无权限访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            await database_1.prisma.image.update({
                where: { id: imageId },
                data: { isDeleted: true }
            });
            res.json({
                success: true,
                message: '图片删除成功'
            });
        }
        catch (error) {
            console.error('删除图片错误:', error);
            next(error);
        }
    }
    static async retryUpload(req, res, next) {
        try {
            const userId = req.user?.id;
            const imageId = parseInt(req.params.imageId);
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const userImage = await database_1.prisma.userImage.findFirst({
                where: {
                    userId: parseInt(userId),
                    imageId,
                },
                include: {
                    image: true
                }
            });
            if (!userImage) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: 'IMAGE_NOT_FOUND',
                        message: '图片不存在或无权限访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            if (userImage.image.uploadStatus !== 'failed') {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '只能重试失败的上传',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const filePath = `uploads/${userImage.image.fileHash}`;
            await queue_service_1.QueueService.addUploadJob({
                imageId,
                userId: parseInt(userId),
                filePath,
                fileInfo: {
                    originalName: userImage.image.originalName,
                    mimeType: userImage.image.mimeType,
                    size: Number(userImage.image.fileSize),
                    hash: userImage.image.fileHash,
                }
            });
            await database_1.prisma.image.update({
                where: { id: imageId },
                data: { uploadStatus: 'processing' }
            });
            res.json({
                success: true,
                message: '重试上传任务已添加到队列'
            });
        }
        catch (error) {
            console.error('重试上传错误:', error);
            next(error);
        }
    }
    static async getAvailableProviders(req, res) {
        try {
            const userId = req.user?.id;
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const providers = await user_provider_service_1.UserProviderService.getAvailableProviders(parseInt(userId));
            const response = {
                success: true,
                data: {
                    providers: providers.map(provider => ({
                        id: provider.id,
                        name: provider.name,
                        description: provider.description,
                        status: provider.status,
                        priority: provider.priority,
                        maxFileSize: provider.maxFileSize,
                        supportedFormats: provider.supportedFormats,
                        isPremium: provider.isPremium,
                        costPerUpload: provider.costPerUpload,
                        isAvailable: provider.isAvailable,
                        source: provider.source
                    }))
                }
            };
            res.json(response);
        }
        catch (error) {
            console.error('获取用户可用接口失败:', error);
            const errorResponse = {
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户可用接口失败',
                    details: error.message,
                    timestamp: new Date().toISOString()
                }
            };
            res.status(500).json(errorResponse);
        }
    }
}
exports.UploadController = UploadController;
//# sourceMappingURL=upload.controller.js.map