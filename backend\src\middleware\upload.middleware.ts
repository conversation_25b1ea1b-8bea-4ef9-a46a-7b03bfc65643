import multer from 'multer';
import { Request, Response, NextFunction } from 'express';
import { config } from '../config/env';
import { ApiResponse, ErrorCodes } from '../types';
import { FileService } from '../services/file.service';

// 内存存储配置
const storage = multer.memoryStorage();

// 文件过滤器
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // 检查文件类型
  if (!FileService.isValidImageType(file.mimetype)) {
    cb(new Error(`不支持的文件类型: ${file.mimetype}`));
    return;
  }

  cb(null, true);
};

// 基础multer配置
const baseMulterConfig: multer.Options = {
  storage,
  fileFilter,
  limits: {
    fileSize: config.upload.maxFileSize,
    files: 20, // 最大文件数量
    fields: 10, // 最大字段数量
  },
};

// 单文件上传中间件
export const uploadSingle = multer({
  ...baseMulterConfig,
  limits: {
    ...baseMulterConfig.limits,
    files: 1,
  },
}).single('file');

// 多文件上传中间件（支持 files 和 file 字段）
export const uploadMultiple = multer({
  ...baseMulterConfig,
}).fields([
  { name: 'files', maxCount: 20 },
  { name: 'file', maxCount: 20 }
]);

// 上传错误处理中间件
export const handleUploadError = (error: any, req: Request, res: Response, next: NextFunction): void => {
  if (error instanceof multer.MulterError) {
    let errorMessage = '文件上传失败';
    let errorCode = ErrorCodes.INTERNAL_ERROR;

    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        errorMessage = `文件大小超过限制，最大允许 ${Math.round(config.upload.maxFileSize / 1024 / 1024)}MB`;
        errorCode = ErrorCodes.FILE_TOO_LARGE;
        break;
      case 'LIMIT_FILE_COUNT':
        errorMessage = '文件数量超过限制';
        errorCode = ErrorCodes.UPLOAD_LIMIT_EXCEEDED;
        break;
      case 'LIMIT_UNEXPECTED_FILE':
        errorMessage = '意外的文件字段';
        errorCode = ErrorCodes.INVALID_INPUT;
        break;
      case 'LIMIT_PART_COUNT':
        errorMessage = '表单部分数量超过限制';
        errorCode = ErrorCodes.INVALID_INPUT;
        break;
      case 'LIMIT_FIELD_KEY':
        errorMessage = '字段名称过长';
        errorCode = ErrorCodes.INVALID_INPUT;
        break;
      case 'LIMIT_FIELD_VALUE':
        errorMessage = '字段值过长';
        errorCode = ErrorCodes.INVALID_INPUT;
        break;
      case 'LIMIT_FIELD_COUNT':
        errorMessage = '字段数量超过限制';
        errorCode = ErrorCodes.INVALID_INPUT;
        break;
      default:
        errorMessage = `文件上传错误: ${error.message}`;
        break;
    }

    res.status(400).json({
      success: false,
      error: {
        code: errorCode,
        message: errorMessage,
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      }
    } as ApiResponse);
    return;
  }

  if (error.message.includes('不支持的文件类型')) {
    res.status(400).json({
      success: false,
      error: {
        code: ErrorCodes.INVALID_FILE_TYPE,
        message: error.message,
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      }
    } as ApiResponse);
    return;
  }

  // 其他错误
  console.error('文件上传中间件错误:', error);
  res.status(500).json({
    success: false,
    error: {
      code: ErrorCodes.INTERNAL_ERROR,
      message: '文件上传处理失败',
      timestamp: new Date().toISOString(),
      requestId: req.requestId,
    }
  } as ApiResponse);
};

// 文件验证中间件
export const validateUploadedFile = (req: Request, res: Response, next: NextFunction): void => {
  const file = req.file;
  
  if (!file) {
    res.status(400).json({
      success: false,
      error: {
        code: ErrorCodes.INVALID_INPUT,
        message: '文件不能为空',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      }
    } as ApiResponse);
    return;
  }

  // 验证文件大小
  if (!FileService.isValidFileSize(file.size)) {
    res.status(400).json({
      success: false,
      error: {
        code: ErrorCodes.FILE_TOO_LARGE,
        message: `文件大小超过限制，最大允许 ${Math.round(config.upload.maxFileSize / 1024 / 1024)}MB`,
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      }
    } as ApiResponse);
    return;
  }

  // 验证文件类型
  if (!FileService.isValidImageType(file.mimetype)) {
    res.status(400).json({
      success: false,
      error: {
        code: ErrorCodes.INVALID_FILE_TYPE,
        message: `不支持的文件类型: ${file.mimetype}`,
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      }
    } as ApiResponse);
    return;
  }

  // 验证文件名
  if (!file.originalname || file.originalname.trim() === '') {
    res.status(400).json({
      success: false,
      error: {
        code: ErrorCodes.INVALID_INPUT,
        message: '文件名不能为空',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      }
    } as ApiResponse);
    return;
  }

  // 检查文件名长度
  if (file.originalname.length > 255) {
    res.status(400).json({
      success: false,
      error: {
        code: ErrorCodes.INVALID_INPUT,
        message: '文件名过长，最大允许255个字符',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      }
    } as ApiResponse);
    return;
  }

  // 检查文件内容是否为空
  if (file.size === 0) {
    res.status(400).json({
      success: false,
      error: {
        code: ErrorCodes.INVALID_INPUT,
        message: '文件内容不能为空',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      }
    } as ApiResponse);
    return;
  }

  next();
};

// 批量文件验证中间件
export const validateUploadedFiles = (req: Request, res: Response, next: NextFunction): void => {
  const files = req.files as Express.Multer.File[];
  
  if (!files || files.length === 0) {
    res.status(400).json({
      success: false,
      error: {
        code: ErrorCodes.INVALID_INPUT,
        message: '文件不能为空',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      }
    } as ApiResponse);
    return;
  }

  // 检查文件数量限制
  if (files.length > 20) {
    res.status(400).json({
      success: false,
      error: {
        code: ErrorCodes.UPLOAD_LIMIT_EXCEEDED,
        message: '一次最多上传20个文件',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      }
    } as ApiResponse);
    return;
  }

  // 验证每个文件
  for (let i = 0; i < files.length; i++) {
    const file = files[i];

    if (!file) {
      res.status(400).json({
        success: false,
        error: {
          code: ErrorCodes.INVALID_FILE,
          message: '文件不能为空',
          timestamp: new Date().toISOString(),
        }
      });
      return;
    }

    // 验证文件大小
    if (!FileService.isValidFileSize(file.size)) {
      res.status(400).json({
        success: false,
        error: {
          code: ErrorCodes.FILE_TOO_LARGE,
          message: `文件 "${file.originalname}" 大小超过限制，最大允许 ${Math.round(config.upload.maxFileSize / 1024 / 1024)}MB`,
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      } as ApiResponse);
      return;
    }

    // 验证文件类型
    if (!FileService.isValidImageType(file.mimetype)) {
      res.status(400).json({
        success: false,
        error: {
          code: ErrorCodes.INVALID_FILE_TYPE,
          message: `文件 "${file.originalname}" 类型不支持: ${file.mimetype}`,
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      } as ApiResponse);
      return;
    }

    // 验证文件名
    if (!file.originalname || file.originalname.trim() === '') {
      res.status(400).json({
        success: false,
        error: {
          code: ErrorCodes.INVALID_INPUT,
          message: `第${i + 1}个文件名不能为空`,
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      } as ApiResponse);
      return;
    }

    // 检查文件内容是否为空
    if (file.size === 0) {
      res.status(400).json({
        success: false,
        error: {
          code: ErrorCodes.INVALID_INPUT,
          message: `文件 "${file.originalname}" 内容不能为空`,
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      } as ApiResponse);
      return;
    }
  }

  next();
};

// 上传限制检查中间件
export const checkUploadLimits = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          code: ErrorCodes.UNAUTHORIZED,
          message: '未授权访问',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      } as ApiResponse);
      return;
    }

    // 这里可以添加更多的上传限制检查逻辑
    // 例如：检查用户今日上传次数、存储空间使用情况等

    next();
  } catch (error) {
    console.error('检查上传限制失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: ErrorCodes.INTERNAL_ERROR,
        message: '检查上传限制失败',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      }
    } as ApiResponse);
  }
};
