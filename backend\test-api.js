const axios = require('axios');

async function testUploadHistory() {
  try {
    console.log('🔍 测试上传历史API...');
    
    // 这里需要一个有效的token，你可以从浏览器开发者工具中获取
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInVzZXJuYW1lIjoidGVzdCIsInJvbGUiOiJ1c2VyIiwiaWF0IjoxNzM1OTc5NzI5LCJleHAiOjE3MzYwNjYxMjl9.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'; // 请替换为实际的token
    
    const response = await axios.get('http://localhost:3000/api/upload/history', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('📊 API响应状态:', response.status);
    console.log('📊 API响应数据:');
    console.log(JSON.stringify(response.data, null, 2));
    
    if (response.data.success && response.data.data.uploads.length > 0) {
      const firstUpload = response.data.data.uploads[0];
      console.log('\n🔍 第一个上传记录详情:');
      console.log('- ID:', firstUpload.id, typeof firstUpload.id);
      console.log('- 文件名:', firstUpload.originalName, typeof firstUpload.originalName);
      console.log('- 文件大小:', firstUpload.fileSize, typeof firstUpload.fileSize);
      console.log('- MIME类型:', firstUpload.mimeType, typeof firstUpload.mimeType);
      console.log('- 创建时间:', firstUpload.createdAt, typeof firstUpload.createdAt);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

testUploadHistory();
