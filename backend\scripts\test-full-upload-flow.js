/**
 * 完整上传流程测试脚本
 * 测试通过正常的上传控制器使用 TCL 接口
 */

const { PrismaClient } = require('@prisma/client');
const { UploadProviderService } = require('../dist/services/upload-provider.service');

const prisma = new PrismaClient();

async function testFullUploadFlow() {
  try {
    console.log('🔄 开始测试完整上传流程...\n');

    // 1. 获取一个测试用户
    let testUser = await prisma.user.findFirst({
      where: {
        userLevel: 'free' // 使用免费用户测试
      }
    });

    if (!testUser) {
      console.log('⚠️  未找到测试用户，创建一个测试用户...');

      testUser = await prisma.user.create({
        data: {
          username: 'tcl_test_user',
          email: '<EMAIL>',
          passwordHash: 'test_password_hash',
          userLevel: 'free',
          status: 'active'
        }
      });

      console.log('✅ 测试用户创建成功:', {
        id: testUser.id,
        username: testUser.username,
        userLevel: testUser.userLevel
      });
    } else {
      console.log('👤 使用现有测试用户:', {
        id: testUser.id,
        username: testUser.username,
        userLevel: testUser.userLevel
      });
    }

    // 2. 获取用户可用的上传接口（包括TCL）
    console.log('\n📋 获取用户可用的上传接口...');
    const availableProviders = await UploadProviderService.getAvailableProviders(
      testUser.id,
      testUser.userLevel
    );

    console.log(`找到 ${availableProviders.length} 个可用接口:`);
    availableProviders.forEach((provider, index) => {
      console.log(`  ${index + 1}. ${provider.name} (ID: ${provider.id}) - ${provider.isPremium ? '高级' : '免费'}`);
    });

    // 3. 查找TCL接口
    const tclProvider = availableProviders.find(p => p.name === 'TCL');
    
    if (!tclProvider) {
      throw new Error('TCL接口不在用户可用接口列表中');
    }

    console.log('\n✅ TCL接口可用:', {
      id: tclProvider.id,
      name: tclProvider.name,
      endpoint: tclProvider.endpoint,
      maxFileSize: tclProvider.maxFileSize,
      supportedFormats: tclProvider.supportedFormats
    });

    // 4. 测试多接口上传（包括TCL）
    console.log('\n🚀 测试多接口上传...');
    
    const testImageBuffer = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );

    const uploadResults = await UploadProviderService.uploadToMultipleProviders(
      [tclProvider], // 只测试TCL接口
      testImageBuffer,
      'full-flow-test.png',
      'image/png'
    );

    console.log('\n📊 多接口上传结果:');
    uploadResults.forEach((result, index) => {
      console.log(`接口 ${index + 1} (${result.providerName}):`);
      console.log(`  成功: ${result.success ? '✅' : '❌'}`);
      console.log(`  URL: ${result.url || 'N/A'}`);
      console.log(`  错误: ${result.error || 'N/A'}`);
      console.log(`  响应时间: ${result.responseTime || 'N/A'}ms`);
      console.log('');
    });

    // 5. 统计成功率
    const successfulUploads = uploadResults.filter(r => r.success);
    const successRate = (successfulUploads.length / uploadResults.length) * 100;

    console.log(`📈 上传成功率: ${successRate.toFixed(1)}% (${successfulUploads.length}/${uploadResults.length})`);

    if (successfulUploads.length > 0) {
      console.log('\n✅ 完整上传流程测试成功！');
      console.log('成功的上传结果:');
      successfulUploads.forEach((result, index) => {
        console.log(`  ${index + 1}. ${result.providerName}: ${result.url}`);
      });
    } else {
      console.log('\n❌ 完整上传流程测试失败，所有接口都上传失败');
    }

    // 6. 验证TCL接口特有的响应处理
    const tclResult = uploadResults.find(r => r.providerName === 'TCL');
    if (tclResult && tclResult.success) {
      console.log('\n🔍 TCL接口特殊验证:');
      console.log('  ✅ 成功提取响应中的data字段');
      console.log('  ✅ URL格式正确');
      
      // 验证URL是否包含预期的域名模式
      if (tclResult.url && tclResult.url.includes('koyoo.cn')) {
        console.log('  ✅ URL域名符合预期');
      } else {
        console.log('  ⚠️  URL域名可能有变化');
      }
    }

  } catch (error) {
    console.error('❌ 完整上传流程测试失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 执行测试
if (require.main === module) {
  testFullUploadFlow()
    .then(() => {
      console.log('\n🎯 完整流程测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 完整流程测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testFullUploadFlow };
