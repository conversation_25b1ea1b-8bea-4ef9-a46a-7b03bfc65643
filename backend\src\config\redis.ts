import Redis, { RedisOptions } from 'ioredis';

// Redis连接配置
const redisConfig: RedisOptions = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  maxRetriesPerRequest: null, // BullMQ 要求设置为 null
  lazyConnect: true,
  keepAlive: 30000,
  connectTimeout: 10000,
  commandTimeout: 5000,
};

// 添加密码（如果存在）
if (process.env.REDIS_PASSWORD) {
  redisConfig.password = process.env.REDIS_PASSWORD;
}

// 添加TLS配置（如果需要）
if (process.env.REDIS_TLS === 'true') {
  redisConfig.tls = {};
}

// 创建Redis实例
export const redis = new Redis(redisConfig);

// Redis连接事件监听
redis.on('connect', () => {
  console.log('✅ Redis连接成功');
});

redis.on('error', (error) => {
  console.error('❌ Redis连接错误:', error);
});

redis.on('close', () => {
  console.log('⚠️ Redis连接已关闭');
});

redis.on('reconnecting', () => {
  console.log('🔄 Redis重新连接中...');
});

// 测试Redis连接
export async function connectRedis(): Promise<void> {
  try {
    await redis.ping();
    console.log('✅ Redis连接测试成功');
  } catch (error) {
    console.error('❌ Redis连接测试失败:', error);
    throw error;
  }
}

// 优雅关闭Redis连接
export async function disconnectRedis(): Promise<void> {
  try {
    await redis.quit();
    console.log('✅ Redis连接已关闭');
  } catch (error) {
    console.error('❌ 关闭Redis连接时出错:', error);
  }
}

// Redis工具函数
export class RedisService {
  // 设置缓存
  static async set(key: string, value: any, ttl?: number): Promise<void> {
    const serializedValue = JSON.stringify(value);
    if (ttl) {
      await redis.setex(key, ttl, serializedValue);
    } else {
      await redis.set(key, serializedValue);
    }
  }

  // 获取缓存
  static async get<T>(key: string): Promise<T | null> {
    const value = await redis.get(key);
    if (!value) return null;
    
    try {
      return JSON.parse(value) as T;
    } catch {
      return value as T;
    }
  }

  // 删除缓存
  static async del(key: string): Promise<void> {
    await redis.del(key);
  }

  // 检查键是否存在
  static async exists(key: string): Promise<boolean> {
    const result = await redis.exists(key);
    return result === 1;
  }

  // 设置过期时间
  static async expire(key: string, ttl: number): Promise<void> {
    await redis.expire(key, ttl);
  }

  // 增加计数器
  static async incr(key: string): Promise<number> {
    return await redis.incr(key);
  }

  // 减少计数器
  static async decr(key: string): Promise<number> {
    return await redis.decr(key);
  }
}
