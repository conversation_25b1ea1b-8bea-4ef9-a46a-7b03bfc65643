import { Request, Response, NextFunction } from 'express';
import { prisma } from '../config/database';
import { config } from '../config/env';
import { ApiResponse, UploadResponse, ErrorCodes } from '../types';
import { FileService } from '../services/file.service';
import { ImageProcessingService } from '../services/image-processing.service';
import { UploadProviderService } from '../services/upload-provider.service';
import { UserProviderService } from '../services/user-provider.service';
import { QueueService } from '../services/queue.service';
import crypto from 'crypto';
import path from 'path';
import fs from 'fs/promises';

export class UploadController {
  // 单文件上传
  static async uploadSingle(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const file = req.file;
      const { providerId } = req.body; // 用户选择的接口ID

      if (!userId) {
        res.status(401).json({
          success: false,
          error: {
            code: ErrorCodes.UNAUTHORIZED,
            message: '未授权访问',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      if (!file) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '文件不能为空',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 检查用户上传限制
      const canUpload = await UploadController.checkUploadLimits(parseInt(userId), file.size);
      if (!canUpload.allowed) {
        res.status(403).json({
          success: false,
          error: {
            code: canUpload.errorCode,
            message: canUpload.message,
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 如果指定了接口，检查用户是否有权限使用
      if (providerId) {
        const canUseProvider = await UserProviderService.canUseProvider(parseInt(userId), parseInt(providerId));
        if (!canUseProvider) {
          res.status(403).json({
            success: false,
            error: {
              code: ErrorCodes.FORBIDDEN,
              message: '您没有权限使用指定的接口',
              timestamp: new Date().toISOString(),
            }
          } as ApiResponse);
          return;
        }
      }

      // 计算文件哈希
      const fileBuffer = file.buffer;
      const fileHash = crypto.createHash('sha256').update(fileBuffer).digest('hex');
      const fileMd5 = crypto.createHash('md5').update(fileBuffer).digest('hex');

      // 检查文件是否已存在（去重）
      const existingImage = await prisma.image.findUnique({
        where: { fileHash },
        include: {
          userImages: {
            where: { userId: parseInt(userId) }
          },
          imageLinks: {
            include: {
              provider: true
            }
          }
        }
      });

      if (existingImage) {
        // 文件已存在，创建用户关联
        if (existingImage.userImages.length === 0) {
          await prisma.userImage.create({
            data: {
              userId: parseInt(userId),
              imageId: existingImage.id,
              isOriginalUploader: false,
            }
          });
        }

        // 记录上传日志
        await UploadController.logUpload(parseInt(userId), existingImage.id, 'reuse', req);

        const response: UploadResponse = {
          success: true,
          message: '文件已存在，直接返回链接',
          data: {
            imageId: existingImage.id.toString(),
            systemUrl: existingImage.systemUrl,
            isReused: true,
            uploadTime: new Date().toISOString(),
            fileSize: Number(existingImage.fileSize),
            mimeType: existingImage.mimeType,
          }
        };

        res.json(response);
        return;
      }

      // 处理图片信息
      const imageInfo = await ImageProcessingService.getImageInfo(fileBuffer, file.mimetype);

      // 保存文件到本地
      const fileName = `${fileHash}${path.extname(file.originalname)}`;
      const filePath = await FileService.saveFile(fileBuffer, fileName);

      // 创建图片记录
      const image = await prisma.image.create({
        data: {
          originalName: file.originalname,
          fileHash,
          fileMd5,
          fileSize: BigInt(file.size),
          mimeType: file.mimetype,
          width: imageInfo.width ?? null,
          height: imageInfo.height ?? null,
          systemUrl: `/api/images/${fileHash}`,
          uploadStatus: 'processing',
        }
      });

      // 创建用户图片关联
      await prisma.userImage.create({
        data: {
          userId: parseInt(userId),
          imageId: image.id,
          isOriginalUploader: true,
        }
      });

      // 记录上传日志
      await UploadController.logUpload(parseInt(userId), image.id, 'upload', req);

      // 异步上传到第三方接口
      const jobData = {
        imageId: image.id,
        userId: parseInt(userId),
        filePath,
        providerId: providerId ? parseInt(providerId) : undefined,
        fileInfo: {
          originalName: file.originalname,
          mimeType: file.mimetype,
          size: file.size,
          hash: fileHash,
        }
      };

      await QueueService.addUploadJob(jobData);

      const response: UploadResponse = {
        success: true,
        message: '文件上传成功',
        data: {
          imageId: image.id.toString(),
          systemUrl: image.systemUrl,
          isReused: false,
          uploadTime: new Date().toISOString(),
          fileSize: file.size,
          mimeType: file.mimetype,
        }
      };

      res.status(201).json(response);

    } catch (error) {
      console.error('上传文件错误:', error);
      next(error);
    }
  }

  // 批量文件上传
  static async uploadBatch(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;

      // 处理多种文件字段格式
      let files: Express.Multer.File[] = [];

      // 调试日志
      console.log('📋 批量上传调试信息:');
      console.log('req.files 类型:', typeof req.files);
      console.log('req.files 内容:', req.files);
      console.log('req.file 内容:', req.file);

      if (req.files) {
        if (Array.isArray(req.files)) {
          // 如果是数组格式（使用 .array() 方法）
          files = req.files;
          console.log('✅ 使用数组格式，文件数量:', files.length);
        } else {
          // 如果是对象格式（使用 .fields() 方法）
          const fileFields = req.files as { [fieldname: string]: Express.Multer.File[] };
          files = [
            ...(fileFields.files || []),
            ...(fileFields.file || [])
          ];
          console.log('✅ 使用对象格式，文件数量:', files.length);
          console.log('fileFields.files:', fileFields.files?.length || 0);
          console.log('fileFields.file:', fileFields.file?.length || 0);
        }
      } else if (req.file) {
        // 如果只有单个文件
        files = [req.file];
        console.log('✅ 使用单文件格式，文件数量:', files.length);
      }

      console.log('📊 最终文件数量:', files.length);

      if (!userId) {
        res.status(401).json({
          success: false,
          error: {
            code: ErrorCodes.UNAUTHORIZED,
            message: '未授权访问',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      if (!files || files.length === 0) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '文件不能为空',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      const results = [];
      const errors = [];

      for (const file of files) {
        try {
          // 检查单个文件的上传限制
          const canUpload = await UploadController.checkUploadLimits(parseInt(userId), file.size);
          if (!canUpload.allowed) {
            errors.push({
              filename: file.originalname,
              error: canUpload.message
            });
            continue;
          }

          // 处理单个文件（简化版本，实际应该复用单文件上传逻辑）
          const fileBuffer = file.buffer;
          const fileHash = crypto.createHash('sha256').update(fileBuffer).digest('hex');

          // 检查是否已存在
          const existingImage = await prisma.image.findUnique({
            where: { fileHash }
          });

          if (existingImage) {
            results.push({
              filename: file.originalname,
              imageId: existingImage.id.toString(),
              systemUrl: existingImage.systemUrl,
              isReused: true
            });
          } else {
            // 创建新图片记录（简化版本）
            const imageInfo = await ImageProcessingService.getImageInfo(fileBuffer, file.mimetype);
            const fileName = `${fileHash}${path.extname(file.originalname)}`;
            const filePath = await FileService.saveFile(fileBuffer, fileName);

            const image = await prisma.image.create({
              data: {
                originalName: file.originalname,
                fileHash,
                fileMd5: crypto.createHash('md5').update(fileBuffer).digest('hex'),
                fileSize: BigInt(file.size),
                mimeType: file.mimetype,
                width: imageInfo.width ?? null,
                height: imageInfo.height ?? null,
                systemUrl: `/api/images/${fileHash}`,
                uploadStatus: 'processing',
              }
            });

            await prisma.userImage.create({
              data: {
                userId: parseInt(userId),
                imageId: image.id,
                isOriginalUploader: true,
              }
            });

            results.push({
              filename: file.originalname,
              imageId: image.id.toString(),
              systemUrl: image.systemUrl,
              isReused: false
            });

            // 添加到上传队列
            await QueueService.addUploadJob({
              imageId: image.id,
              userId: parseInt(userId),
              filePath,
              fileInfo: {
                originalName: file.originalname,
                mimeType: file.mimetype,
                size: file.size,
                hash: fileHash,
              }
            });
          }

        } catch (fileError) {
          console.error(`处理文件 ${file.originalname} 时出错:`, fileError);
          errors.push({
            filename: file.originalname,
            error: '文件处理失败'
          });
        }
      }

      res.json({
        success: true,
        message: `批量上传完成，成功: ${results.length}，失败: ${errors.length}`,
        data: {
          results,
          errors,
          summary: {
            total: files.length,
            success: results.length,
            failed: errors.length
          }
        }
      } as ApiResponse);

    } catch (error) {
      console.error('批量上传错误:', error);
      next(error);
    }
  }

  // 检查用户上传限制
  private static async checkUploadLimits(userId: number, fileSize: number): Promise<{
    allowed: boolean;
    errorCode?: string;
    message?: string;
  }> {
    try {
      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { userLevel: true }
      });

      if (!user) {
        return {
          allowed: false,
          errorCode: 'USER_NOT_FOUND',
          message: '用户不存在'
        };
      }

      // 获取用户等级配置
      const levelConfig = await prisma.userLevelConfig.findUnique({
        where: { level: user.userLevel }
      });

      if (!levelConfig) {
        return {
          allowed: false,
          errorCode: 'LEVEL_CONFIG_NOT_FOUND',
          message: '用户等级配置不存在'
        };
      }

      // 检查文件大小限制
      if (fileSize > Number(levelConfig.maxFileSize)) {
        return {
          allowed: false,
          errorCode: ErrorCodes.FILE_TOO_LARGE,
          message: `文件大小超过限制，最大允许 ${Math.round(Number(levelConfig.maxFileSize) / 1024 / 1024)}MB`
        };
      }

      // 检查今日上传次数限制
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const todayUploads = await prisma.uploadLog.count({
        where: {
          userId,
          actionType: 'upload',
          isSuccess: true,
          createdAt: {
            gte: today,
            lt: tomorrow
          }
        }
      });

      if (todayUploads >= levelConfig.maxDailyUploads) {
        return {
          allowed: false,
          errorCode: ErrorCodes.UPLOAD_LIMIT_EXCEEDED,
          message: `今日上传次数已达上限 ${levelConfig.maxDailyUploads} 次`
        };
      }

      return { allowed: true };

    } catch (error) {
      console.error('检查上传限制时出错:', error);
      return {
        allowed: false,
        errorCode: ErrorCodes.INTERNAL_ERROR,
        message: '检查上传限制时出错'
      };
    }
  }

  // 记录上传日志
  private static async logUpload(
    userId: number,
    imageId: number,
    actionType: string,
    req: Request
  ): Promise<void> {
    try {
      const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent') || '';

      await prisma.uploadLog.create({
        data: {
          userId,
          imageId,
          actionType,
          ipAddress: clientIP,
          userAgent,
          isSuccess: true,
        }
      });
    } catch (error) {
      console.error('记录上传日志失败:', error);
      // 不抛出错误，避免影响主要流程
    }
  }

  // 获取用户上传限制信息
  static async getUploadLimits(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: {
            code: ErrorCodes.UNAUTHORIZED,
            message: '未授权访问',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) },
        select: { userLevel: true }
      });

      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: '用户不存在',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      const levelConfig = await prisma.userLevelConfig.findUnique({
        where: { level: user.userLevel }
      });

      if (!levelConfig) {
        res.status(404).json({
          success: false,
          error: {
            code: 'LEVEL_CONFIG_NOT_FOUND',
            message: '用户等级配置不存在',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 获取今日上传次数
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const todayUploads = await prisma.uploadLog.count({
        where: {
          userId: parseInt(userId),
          actionType: 'upload',
          isSuccess: true,
          createdAt: {
            gte: today,
            lt: tomorrow
          }
        }
      });

      res.json({
        success: true,
        data: {
          userLevel: user.userLevel,
          maxDailyUploads: levelConfig.maxDailyUploads,
          maxFileSize: Number(levelConfig.maxFileSize),
          maxStorageSpace: Number(levelConfig.maxStorageSpace),
          todayUploads,
          remainingUploads: Math.max(0, levelConfig.maxDailyUploads - todayUploads),
          canChooseProvider: levelConfig.canChooseProvider,
          visibleProviderCount: levelConfig.visibleProviderCount,
        }
      } as ApiResponse);

    } catch (error) {
      console.error('获取上传限制信息错误:', error);
      next(error);
    }
  }

  // 获取用户上传历史
  static async getUploadHistory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const offset = (page - 1) * limit;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: {
            code: ErrorCodes.UNAUTHORIZED,
            message: '未授权访问',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      const [uploads, total] = await Promise.all([
        prisma.userImage.findMany({
          where: { userId: parseInt(userId) },
          include: {
            image: {
              include: {
                imageLinks: {
                  include: {
                    provider: true
                  }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip: offset,
          take: limit,
        }),
        prisma.userImage.count({
          where: { userId: parseInt(userId) }
        })
      ]);

      const uploadHistory = uploads.map(upload => ({
        id: upload.image.id,
        originalName: upload.image.originalName,
        fileSize: Number(upload.image.fileSize),
        mimeType: upload.image.mimeType,
        systemUrl: upload.image.systemUrl,
        uploadStatus: upload.image.uploadStatus,
        isOriginalUploader: upload.isOriginalUploader,
        accessCount: upload.accessCount,
        createdAt: upload.createdAt.toISOString(),
        links: upload.image.imageLinks.map(link => ({
          provider: link.provider.name,
          url: link.externalUrl,
          status: link.status,
        }))
      }));

      res.json({
        success: true,
        data: {
          uploads: uploadHistory,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
          }
        }
      } as ApiResponse);

    } catch (error) {
      console.error('获取上传历史错误:', error);
      next(error);
    }
  }

  // 获取用户上传统计
  static async getUploadStats(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: {
            code: ErrorCodes.UNAUTHORIZED,
            message: '未授权访问',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      const [totalUploads, totalSize, todayUploads, thisMonthUploads] = await Promise.all([
        prisma.userImage.count({
          where: { userId: parseInt(userId) }
        }),
        prisma.image.aggregate({
          where: {
            userImages: {
              some: { userId: parseInt(userId) }
            }
          },
          _sum: { fileSize: true }
        }),
        prisma.uploadLog.count({
          where: {
            userId: parseInt(userId),
            actionType: 'upload',
            isSuccess: true,
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0))
            }
          }
        }),
        prisma.uploadLog.count({
          where: {
            userId: parseInt(userId),
            actionType: 'upload',
            isSuccess: true,
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          }
        })
      ]);

      res.json({
        success: true,
        data: {
          totalUploads,
          totalSize: Number(totalSize._sum?.fileSize || 0),
          todayUploads,
          thisMonthUploads,
        }
      } as ApiResponse);

    } catch (error) {
      console.error('获取上传统计错误:', error);
      next(error);
    }
  }

  // 删除上传的图片
  static async deleteUpload(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const imageId = parseInt(req.params.imageId as string);

      if (!userId) {
        res.status(401).json({
          success: false,
          error: {
            code: ErrorCodes.UNAUTHORIZED,
            message: '未授权访问',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 检查用户是否有权限删除此图片
      const userImage = await prisma.userImage.findFirst({
        where: {
          userId: parseInt(userId),
          imageId,
        },
        include: {
          image: true
        }
      });

      if (!userImage) {
        res.status(404).json({
          success: false,
          error: {
            code: 'IMAGE_NOT_FOUND',
            message: '图片不存在或无权限访问',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 软删除图片
      await prisma.image.update({
        where: { id: imageId },
        data: { isDeleted: true }
      });

      res.json({
        success: true,
        message: '图片删除成功'
      } as ApiResponse);

    } catch (error) {
      console.error('删除图片错误:', error);
      next(error);
    }
  }

  // 重试失败的上传
  static async retryUpload(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const imageId = parseInt(req.params.imageId as string);

      if (!userId) {
        res.status(401).json({
          success: false,
          error: {
            code: ErrorCodes.UNAUTHORIZED,
            message: '未授权访问',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 检查图片是否存在且属于用户
      const userImage = await prisma.userImage.findFirst({
        where: {
          userId: parseInt(userId),
          imageId,
        },
        include: {
          image: true
        }
      });

      if (!userImage) {
        res.status(404).json({
          success: false,
          error: {
            code: 'IMAGE_NOT_FOUND',
            message: '图片不存在或无权限访问',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      if (userImage.image.uploadStatus !== 'failed') {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '只能重试失败的上传',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 重新添加到上传队列
      const filePath = `uploads/${userImage.image.fileHash}`;
      await QueueService.addUploadJob({
        imageId,
        userId: parseInt(userId),
        filePath,
        fileInfo: {
          originalName: userImage.image.originalName,
          mimeType: userImage.image.mimeType,
          size: Number(userImage.image.fileSize),
          hash: userImage.image.fileHash,
        }
      });

      // 更新图片状态
      await prisma.image.update({
        where: { id: imageId },
        data: { uploadStatus: 'processing' }
      });

      res.json({
        success: true,
        message: '重试上传任务已添加到队列'
      } as ApiResponse);

    } catch (error) {
      console.error('重试上传错误:', error);
      next(error);
    }
  }

  /**
   * 获取用户可用的接口列表
   */
  static async getAvailableProviders(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: {
            code: ErrorCodes.UNAUTHORIZED,
            message: '未授权访问',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      const providers = await UserProviderService.getAvailableProviders(parseInt(userId));

      const response: ApiResponse<{ providers: any[] }> = {
        success: true,
        data: {
          providers: providers.map(provider => ({
            id: provider.id,
            name: provider.name,
            description: provider.description,
            status: provider.status,
            priority: provider.priority,
            maxFileSize: provider.maxFileSize,
            supportedFormats: provider.supportedFormats,
            isPremium: provider.isPremium,
            costPerUpload: provider.costPerUpload,
            isAvailable: provider.isAvailable,
            source: provider.source
          }))
        }
      };

      res.json(response);
    } catch (error) {
      console.error('获取用户可用接口失败:', error);

      const errorResponse: ApiResponse<null> = {
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取用户可用接口失败',
          details: (error as Error).message,
          timestamp: new Date().toISOString()
        }
      };

      res.status(500).json(errorResponse);
    }
  }
}
