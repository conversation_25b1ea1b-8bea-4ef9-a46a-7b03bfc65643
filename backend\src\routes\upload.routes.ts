import { Router, Request, Response, NextFunction } from 'express';
import { UploadController } from '../controllers/upload.controller';
import { authenticateToken, requireLevel, requestId, recordIP } from '../middleware/auth.middleware';
import { 
  uploadSingle, 
  uploadMultiple, 
  handleUploadError, 
  validateUploadedFile, 
  validateUploadedFiles,
  checkUploadLimits 
} from '../middleware/upload.middleware';
// import { validateJoiRequest } from '../middleware/validation.middleware';
// import { uploadValidation } from '../utils/validation.schemas';

const router = Router();

// 应用通用中间件
router.use(requestId);
router.use(recordIP);

/**
 * @route   POST /api/upload/single
 * @desc    单文件上传
 * @access  Private
 */
router.post('/single',
  authenticateToken,
  checkUploadLimits,
  uploadSingle,
  handleUploadError,
  validateUploadedFile,
  // 注意：文件上传不使用 Joi 验证，因为 req.body 在 multer 处理后可能包含非 JSON 数据
  UploadController.uploadSingle
);

/**
 * @route   POST /api/upload/batch
 * @desc    批量文件上传
 * @access  Private
 */
router.post('/batch',
  (req: Request, res: Response, next: NextFunction) => {
    console.log('🚀 批量上传路由开始处理请求');
    console.log('Content-Type:', req.get('Content-Type'));
    console.log('Content-Length:', req.get('Content-Length'));
    next();
  },
  authenticateToken,
  requireLevel('vip1'), // 批量上传需要VIP1或更高等级
  checkUploadLimits,
  uploadMultiple,
  handleUploadError,
  validateUploadedFiles,
  // 注意：文件上传不使用 Joi 验证，因为 req.body 在 multer 处理后可能包含非 JSON 数据
  UploadController.uploadBatch
);

/**
 * @route   GET /api/upload/limits
 * @desc    获取用户上传限制信息
 * @access  Private
 */
router.get('/limits',
  authenticateToken,
  UploadController.getUploadLimits
);

/**
 * @route   GET /api/upload/history
 * @desc    获取用户上传历史
 * @access  Private
 */
router.get('/history',
  authenticateToken,
  UploadController.getUploadHistory
);

/**
 * @route   GET /api/upload/stats
 * @desc    获取用户上传统计
 * @access  Private
 */
router.get('/stats',
  authenticateToken,
  UploadController.getUploadStats
);

/**
 * @route   DELETE /api/upload/:imageId
 * @desc    删除上传的图片
 * @access  Private
 */
router.delete('/:imageId',
  authenticateToken,
  UploadController.deleteUpload
);

/**
 * @route   POST /api/upload/retry/:imageId
 * @desc    重试失败的上传
 * @access  Private
 */
router.post('/retry/:imageId',
  authenticateToken,
  UploadController.retryUpload
);

/**
 * @route   GET /api/upload/providers
 * @desc    获取用户可用的接口列表
 * @access  Private
 */
router.get('/providers',
  authenticateToken,
  UploadController.getAvailableProviders
);

export default router;
