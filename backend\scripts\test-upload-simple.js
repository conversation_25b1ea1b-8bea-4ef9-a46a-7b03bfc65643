/**
 * 简单的上传测试脚本
 * 使用现有用户的 token 测试上传
 */

const FormData = require('form-data');
const axios = require('axios');
const { PrismaClient } = require('@prisma/client');
const jwt = require('jsonwebtoken');

const prisma = new PrismaClient();

async function testSimpleUpload() {
  try {
    console.log('🧪 开始简单上传测试...\n');

    // 1. 获取一个现有用户并生成 token
    console.log('👤 获取测试用户...');
    
    const testUser = await prisma.user.findFirst({
      where: {
        status: 'active'
      }
    });

    if (!testUser) {
      throw new Error('未找到可用的测试用户');
    }

    console.log('✅ 找到测试用户:', {
      id: testUser.id,
      username: testUser.username,
      userLevel: testUser.userLevel
    });

    // 2. 生成 JWT token
    const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
    const token = jwt.sign(
      { 
        userId: testUser.id,
        username: testUser.username,
        role: testUser.role 
      },
      JWT_SECRET,
      { expiresIn: '1h' }
    );

    console.log('✅ 生成 token 成功');

    // 3. 创建测试图片
    console.log('\n🖼️  准备测试图片...');
    
    const testImageBuffer = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );

    console.log('图片信息:', {
      size: testImageBuffer.length + ' bytes',
      type: 'image/png'
    });

    // 4. 准备 FormData
    const formData = new FormData();
    formData.append('file', testImageBuffer, {
      filename: 'simple-test.png',
      contentType: 'image/png'
    });

    // 指定使用 TCL 接口
    formData.append('providerId', '4');

    console.log('\n📤 发送上传请求...');
    console.log('目标: http://localhost:3000/api/upload/single');

    // 5. 发送上传请求
    const startTime = Date.now();
    
    const uploadResponse = await axios.post(
      'http://localhost:3000/api/upload/single',
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${token}`
        },
        timeout: 30000
      }
    );

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log('\n📊 上传结果:');
    console.log('状态码:', uploadResponse.status);
    console.log('响应时间:', responseTime + 'ms');
    console.log('响应数据:');
    console.log(JSON.stringify(uploadResponse.data, null, 2));

    if (uploadResponse.data.success) {
      console.log('\n🎉 上传测试成功！');
      console.log('✅ 修复生效：JSON 解析错误已解决');
      
      const data = uploadResponse.data.data;
      console.log('\n📋 上传详情:');
      console.log('- 图片ID:', data.imageId);
      console.log('- 系统URL:', data.systemUrl);
      console.log('- 是否复用:', data.isReused ? '是' : '否');
      console.log('- 上传时间:', data.uploadTime);
      console.log('- 文件大小:', data.fileSize + ' bytes');
      console.log('- MIME类型:', data.mimeType);
      
    } else {
      console.log('\n❌ 上传失败');
      console.log('错误信息:', uploadResponse.data.error);
    }

  } catch (error) {
    console.error('\n💥 测试失败:', error.message);
    
    if (error.response) {
      console.error('\n📋 错误详情:');
      console.error('状态码:', error.response.status);
      console.error('响应头:', error.response.headers);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
      
      // 分析错误类型
      if (error.response.data?.error?.message?.includes('JSON')) {
        console.error('\n🔍 分析: 仍然存在 JSON 解析问题，需要进一步修复');
      } else if (error.response.status === 401) {
        console.error('\n🔍 分析: 认证问题');
      } else if (error.response.status === 400) {
        console.error('\n🔍 分析: 请求参数问题');
      }
    }
    
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 执行测试
if (require.main === module) {
  testSimpleUpload()
    .then(() => {
      console.log('\n✨ 简单上传测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 简单上传测试失败');
      process.exit(1);
    });
}

module.exports = { testSimpleUpload };
