/**
 * 最小化测试脚本
 */

const axios = require('axios');

async function testMinimal() {
  try {
    console.log('🧪 测试服务器连接...');
    
    // 1. 测试健康检查
    const healthResponse = await axios.get('http://localhost:3000/health');
    console.log('✅ 健康检查成功:', healthResponse.data);

    // 2. 测试 API 根路径
    const apiResponse = await axios.get('http://localhost:3000/api');
    console.log('✅ API 根路径成功:', apiResponse.data);

    console.log('\n🎉 服务器连接正常');

  } catch (error) {
    console.error('❌ 连接失败:', error.message);
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('响应:', error.response.data);
    }
  }
}

testMinimal();
