"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkUploadLimits = exports.validateUploadedFiles = exports.validateUploadedFile = exports.handleUploadError = exports.uploadMultiple = exports.uploadSingle = void 0;
const multer_1 = __importDefault(require("multer"));
const env_1 = require("../config/env");
const types_1 = require("../types");
const file_service_1 = require("../services/file.service");
const storage = multer_1.default.memoryStorage();
const fileFilter = (req, file, cb) => {
    if (!file_service_1.FileService.isValidImageType(file.mimetype)) {
        cb(new Error(`不支持的文件类型: ${file.mimetype}`));
        return;
    }
    cb(null, true);
};
const baseMulterConfig = {
    storage,
    fileFilter,
    limits: {
        fileSize: env_1.config.upload.maxFileSize,
        files: 20,
        fields: 10,
    },
};
exports.uploadSingle = (0, multer_1.default)({
    ...baseMulterConfig,
    limits: {
        ...baseMulterConfig.limits,
        files: 1,
    },
}).single('file');
exports.uploadMultiple = (0, multer_1.default)({
    ...baseMulterConfig,
}).fields([
    { name: 'files', maxCount: 20 },
    { name: 'file', maxCount: 20 }
]);
const handleUploadError = (error, req, res, next) => {
    if (error instanceof multer_1.default.MulterError) {
        let errorMessage = '文件上传失败';
        let errorCode = types_1.ErrorCodes.INTERNAL_ERROR;
        switch (error.code) {
            case 'LIMIT_FILE_SIZE':
                errorMessage = `文件大小超过限制，最大允许 ${Math.round(env_1.config.upload.maxFileSize / 1024 / 1024)}MB`;
                errorCode = types_1.ErrorCodes.FILE_TOO_LARGE;
                break;
            case 'LIMIT_FILE_COUNT':
                errorMessage = '文件数量超过限制';
                errorCode = types_1.ErrorCodes.UPLOAD_LIMIT_EXCEEDED;
                break;
            case 'LIMIT_UNEXPECTED_FILE':
                errorMessage = '意外的文件字段';
                errorCode = types_1.ErrorCodes.INVALID_INPUT;
                break;
            case 'LIMIT_PART_COUNT':
                errorMessage = '表单部分数量超过限制';
                errorCode = types_1.ErrorCodes.INVALID_INPUT;
                break;
            case 'LIMIT_FIELD_KEY':
                errorMessage = '字段名称过长';
                errorCode = types_1.ErrorCodes.INVALID_INPUT;
                break;
            case 'LIMIT_FIELD_VALUE':
                errorMessage = '字段值过长';
                errorCode = types_1.ErrorCodes.INVALID_INPUT;
                break;
            case 'LIMIT_FIELD_COUNT':
                errorMessage = '字段数量超过限制';
                errorCode = types_1.ErrorCodes.INVALID_INPUT;
                break;
            default:
                errorMessage = `文件上传错误: ${error.message}`;
                break;
        }
        res.status(400).json({
            success: false,
            error: {
                code: errorCode,
                message: errorMessage,
                timestamp: new Date().toISOString(),
                requestId: req.requestId,
            }
        });
        return;
    }
    if (error.message.includes('不支持的文件类型')) {
        res.status(400).json({
            success: false,
            error: {
                code: types_1.ErrorCodes.INVALID_FILE_TYPE,
                message: error.message,
                timestamp: new Date().toISOString(),
                requestId: req.requestId,
            }
        });
        return;
    }
    console.error('文件上传中间件错误:', error);
    res.status(500).json({
        success: false,
        error: {
            code: types_1.ErrorCodes.INTERNAL_ERROR,
            message: '文件上传处理失败',
            timestamp: new Date().toISOString(),
            requestId: req.requestId,
        }
    });
};
exports.handleUploadError = handleUploadError;
const validateUploadedFile = (req, res, next) => {
    const file = req.file;
    if (!file) {
        res.status(400).json({
            success: false,
            error: {
                code: types_1.ErrorCodes.INVALID_INPUT,
                message: '文件不能为空',
                timestamp: new Date().toISOString(),
                requestId: req.requestId,
            }
        });
        return;
    }
    if (!file_service_1.FileService.isValidFileSize(file.size)) {
        res.status(400).json({
            success: false,
            error: {
                code: types_1.ErrorCodes.FILE_TOO_LARGE,
                message: `文件大小超过限制，最大允许 ${Math.round(env_1.config.upload.maxFileSize / 1024 / 1024)}MB`,
                timestamp: new Date().toISOString(),
                requestId: req.requestId,
            }
        });
        return;
    }
    if (!file_service_1.FileService.isValidImageType(file.mimetype)) {
        res.status(400).json({
            success: false,
            error: {
                code: types_1.ErrorCodes.INVALID_FILE_TYPE,
                message: `不支持的文件类型: ${file.mimetype}`,
                timestamp: new Date().toISOString(),
                requestId: req.requestId,
            }
        });
        return;
    }
    if (!file.originalname || file.originalname.trim() === '') {
        res.status(400).json({
            success: false,
            error: {
                code: types_1.ErrorCodes.INVALID_INPUT,
                message: '文件名不能为空',
                timestamp: new Date().toISOString(),
                requestId: req.requestId,
            }
        });
        return;
    }
    if (file.originalname.length > 255) {
        res.status(400).json({
            success: false,
            error: {
                code: types_1.ErrorCodes.INVALID_INPUT,
                message: '文件名过长，最大允许255个字符',
                timestamp: new Date().toISOString(),
                requestId: req.requestId,
            }
        });
        return;
    }
    if (file.size === 0) {
        res.status(400).json({
            success: false,
            error: {
                code: types_1.ErrorCodes.INVALID_INPUT,
                message: '文件内容不能为空',
                timestamp: new Date().toISOString(),
                requestId: req.requestId,
            }
        });
        return;
    }
    next();
};
exports.validateUploadedFile = validateUploadedFile;
const validateUploadedFiles = (req, res, next) => {
    const files = req.files;
    if (!files || files.length === 0) {
        res.status(400).json({
            success: false,
            error: {
                code: types_1.ErrorCodes.INVALID_INPUT,
                message: '文件不能为空',
                timestamp: new Date().toISOString(),
                requestId: req.requestId,
            }
        });
        return;
    }
    if (files.length > 20) {
        res.status(400).json({
            success: false,
            error: {
                code: types_1.ErrorCodes.UPLOAD_LIMIT_EXCEEDED,
                message: '一次最多上传20个文件',
                timestamp: new Date().toISOString(),
                requestId: req.requestId,
            }
        });
        return;
    }
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        if (!file) {
            res.status(400).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INVALID_FILE,
                    message: '文件不能为空',
                    timestamp: new Date().toISOString(),
                }
            });
            return;
        }
        if (!file_service_1.FileService.isValidFileSize(file.size)) {
            res.status(400).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.FILE_TOO_LARGE,
                    message: `文件 "${file.originalname}" 大小超过限制，最大允许 ${Math.round(env_1.config.upload.maxFileSize / 1024 / 1024)}MB`,
                    timestamp: new Date().toISOString(),
                    requestId: req.requestId,
                }
            });
            return;
        }
        if (!file_service_1.FileService.isValidImageType(file.mimetype)) {
            res.status(400).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INVALID_FILE_TYPE,
                    message: `文件 "${file.originalname}" 类型不支持: ${file.mimetype}`,
                    timestamp: new Date().toISOString(),
                    requestId: req.requestId,
                }
            });
            return;
        }
        if (!file.originalname || file.originalname.trim() === '') {
            res.status(400).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INVALID_INPUT,
                    message: `第${i + 1}个文件名不能为空`,
                    timestamp: new Date().toISOString(),
                    requestId: req.requestId,
                }
            });
            return;
        }
        if (file.size === 0) {
            res.status(400).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INVALID_INPUT,
                    message: `文件 "${file.originalname}" 内容不能为空`,
                    timestamp: new Date().toISOString(),
                    requestId: req.requestId,
                }
            });
            return;
        }
    }
    next();
};
exports.validateUploadedFiles = validateUploadedFiles;
const checkUploadLimits = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.UNAUTHORIZED,
                    message: '未授权访问',
                    timestamp: new Date().toISOString(),
                    requestId: req.requestId,
                }
            });
            return;
        }
        next();
    }
    catch (error) {
        console.error('检查上传限制失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: types_1.ErrorCodes.INTERNAL_ERROR,
                message: '检查上传限制失败',
                timestamp: new Date().toISOString(),
                requestId: req.requestId,
            }
        });
    }
};
exports.checkUploadLimits = checkUploadLimits;
//# sourceMappingURL=upload.middleware.js.map