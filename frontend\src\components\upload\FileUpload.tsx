import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '../ui/Button';
import { Card, CardContent } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { upload } from '../../lib/api';
import { formatFileSize, isImageFile } from '../../lib/utils';
import { useToast } from '../ui/Toast';

interface FileUploadProps {
  onUploadSuccess?: () => void;
  onUploadError?: (error: string) => void;
  maxFiles?: number;
  maxFileSize?: number;
}

interface UploadFile extends File {
  id: string;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
}

export function FileUpload({
  onUploadSuccess,
  onUploadError,
  maxFiles = 20,
  maxFileSize = 100 * 1024 * 1024 // 100MB
}: FileUploadProps) {
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [providers, setProviders] = useState<any[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<number | null>(null);
  const [loadingProviders, setLoadingProviders] = useState(false);
  const { addToast } = useToast();

  // 加载用户可用的接口列表
  const loadProviders = useCallback(async () => {
    try {
      setLoadingProviders(true);
      const response = await upload.getProviders();
      if (response.success && response.data) {
        const availableProviders = response.data.providers.filter((p: any) => p.isAvailable);
        setProviders(availableProviders);

        // 默认选择第一个可用接口
        if (availableProviders.length > 0 && !selectedProvider) {
          setSelectedProvider(availableProviders[0].id);
        }
      }
    } catch (error) {
      console.error('加载接口列表失败:', error);
      addToast({
        type: 'error',
        title: '加载失败',
        message: '无法加载可用的上传接口',
      });
    } finally {
      setLoadingProviders(false);
    }
  }, [selectedProvider, addToast]);

  // 组件挂载时加载接口列表
  React.useEffect(() => {
    loadProviders();
  }, [loadProviders]);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // 处理被拒绝的文件
    if (rejectedFiles.length > 0) {
      const errors = rejectedFiles.map(({ file, errors }) =>
        `${file.name}: ${errors.map((e: any) => e.message).join(', ')}`
      );
      addToast({
        type: 'error',
        title: '文件上传失败',
        message: errors.join('\n'),
      });
      onUploadError?.(errors.join('\n'));
    }

    // 添加接受的文件
    const newFiles: UploadFile[] = acceptedFiles.map(file => {
      const uploadFile = Object.assign(file, {
        id: Math.random().toString(36).substr(2, 9),
        progress: 0,
        status: 'pending' as const,
      });
      return uploadFile as UploadFile;
    });

    setFiles(prev => [...prev, ...newFiles]);
  }, [onUploadError]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    maxFiles,
    maxSize: maxFileSize,
    multiple: true,
  });

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const uploadFiles = async () => {
    if (files.length === 0) return;

    setUploading(true);

    try {
      // 获取待上传的文件（状态为 pending 的文件）
      const filesToUpload = files.filter(f => f.status === 'pending');

      if (filesToUpload.length === 0) {
        setUploading(false);
        return;
      }

      // 更新这些文件的状态为上传中
      setFiles(prev => prev.map(f =>
        filesToUpload.some(uploadFile => uploadFile.id === f.id)
          ? { ...f, status: 'uploading' as const }
          : f
      ));
      
      if (filesToUpload.length === 1) {
        // 单文件上传
        const response = await upload.single(filesToUpload[0], selectedProvider || undefined);
        
        if (response.success) {
          setFiles(prev => prev.map(f =>
            f.id === filesToUpload[0].id
              ? { ...f, status: 'success', progress: 100 }
              : f
          ));
          addToast({
            type: 'success',
            title: '上传成功',
            message: `文件 "${filesToUpload[0].name}" 上传成功`,
          });
          onUploadSuccess?.();
        } else {
          throw new Error(response.error?.message || '上传失败');
        }
      } else {
        // 批量上传
        console.log('🚀 前端批量上传调试信息:');
        console.log('filesToUpload 数量:', filesToUpload.length);
        console.log('filesToUpload 详情:', filesToUpload.map(f => ({
          id: f.id,
          name: f.name,
          size: f.size,
          type: f.type,
          status: f.status
        })));

        const response = await upload.batch(filesToUpload);
        
        if (response.success) {
          setFiles(prev => prev.map(f => ({ ...f, status: 'success', progress: 100 })));
          addToast({
            type: 'success',
            title: '批量上传成功',
            message: `成功上传 ${filesToUpload.length} 个文件`,
          });
          onUploadSuccess?.();
        } else {
          throw new Error(response.error?.message || '批量上传失败');
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '上传失败';
      setFiles(prev => prev.map(f => ({
        ...f,
        status: 'error',
        error: errorMessage
      })));
      addToast({
        type: 'error',
        title: '上传失败',
        message: errorMessage,
      });
      onUploadError?.(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  const clearFiles = () => {
    setFiles([]);
  };

  const pendingFiles = files.filter(f => f.status === 'pending');
  const hasFiles = files.length > 0;

  return (
    <div className="space-y-4">
      {/* 上传区域 */}
      <Card>
        <CardContent className="p-0">
          <div
            {...getRootProps()}
            className={`upload-zone ${isDragActive ? 'dragover' : ''}`}
          >
            <input {...getInputProps()} />
            <div className="flex flex-col items-center space-y-4">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>
              <div className="text-center">
                <p className="text-lg font-medium text-gray-900">
                  {isDragActive ? '释放文件以上传' : '拖拽文件到此处或点击选择'}
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  支持 JPEG、PNG、GIF、WebP 格式，单个文件最大 {formatFileSize(maxFileSize)}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 接口选择器 */}
      {providers.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-4">
              <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
                选择上传接口:
              </label>
              <select
                value={selectedProvider || ''}
                onChange={(e) => setSelectedProvider(e.target.value ? parseInt(e.target.value) : null)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                disabled={uploading || loadingProviders}
              >
                <option value="">自动选择最佳接口</option>
                {providers.map((provider) => (
                  <option key={provider.id} value={provider.id}>
                    {provider.name}
                    {provider.isPremium && ' (高级)'}
                    {provider.source === 'manual' && ' (特殊授权)'}
                  </option>
                ))}
              </select>
              {loadingProviders && (
                <div className="loading-spinner w-4 h-4" />
              )}
            </div>
            {selectedProvider && (
              <div className="mt-2 text-xs text-gray-500">
                {(() => {
                  const provider = providers.find(p => p.id === selectedProvider);
                  return provider ? (
                    <span>
                      最大文件: {formatFileSize(provider.maxFileSize)}
                      {provider.costPerUpload > 0 && ` • 费用: $${provider.costPerUpload.toFixed(4)}/次`}
                    </span>
                  ) : null;
                })()}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 文件列表 */}
      {hasFiles && (
        <Card>
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium">待上传文件 ({files.length})</h3>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearFiles}
                  disabled={uploading}
                >
                  清空
                </Button>
                <Button
                  size="sm"
                  onClick={uploadFiles}
                  disabled={uploading || pendingFiles.length === 0}
                  loading={uploading}
                >
                  上传 ({pendingFiles.length})
                </Button>
              </div>
            </div>

            <div className="space-y-3">
              {files.map((file) => (
                <div key={file.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                  <div className="flex-shrink-0">
                    {isImageFile(file) && (
                      <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                        <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {file.name}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatFileSize(file.size)}
                    </p>
                    {file.status === 'uploading' && (
                      <div className="progress-bar mt-2">
                        <div 
                          className="progress-fill" 
                          style={{ width: `${file.progress}%` }}
                        />
                      </div>
                    )}
                    {file.error && (
                      <p className="text-sm text-error-600 mt-1">{file.error}</p>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant={
                        file.status === 'success' ? 'success' :
                        file.status === 'error' ? 'error' :
                        file.status === 'uploading' ? 'warning' : 'secondary'
                      }
                    >
                      {file.status === 'pending' && '待上传'}
                      {file.status === 'uploading' && '上传中'}
                      {file.status === 'success' && '成功'}
                      {file.status === 'error' && '失败'}
                    </Badge>
                    
                    {file.status === 'pending' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(file.id)}
                      >
                        移除
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
