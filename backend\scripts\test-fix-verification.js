/**
 * 验证 JSON 解析修复的测试脚本
 */

const FormData = require('form-data');
const axios = require('axios');

async function testFixVerification() {
  try {
    console.log('🧪 验证 JSON 解析修复...\n');

    // 1. 测试健康检查
    console.log('1. 测试健康检查...');
    const healthResponse = await axios.get('http://localhost:3000/health');
    console.log('✅ 健康检查成功:', healthResponse.data.status);

    // 2. 创建测试图片
    console.log('\n2. 准备测试数据...');
    const testImageBuffer = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );

    // 3. 准备 FormData
    const formData = new FormData();
    formData.append('file', testImageBuffer, {
      filename: 'test-fix.png',
      contentType: 'image/png'
    });

    console.log('✅ 测试数据准备完成');

    // 4. 发送上传请求（不需要认证，只测试 JSON 解析）
    console.log('\n3. 发送上传请求测试 JSON 解析...');
    
    try {
      const uploadResponse = await axios.post(
        'http://localhost:3000/api/upload/single',
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            'Authorization': 'Bearer invalid_token_for_test'
          },
          timeout: 10000
        }
      );

      // 如果到达这里，说明 JSON 解析没有问题
      console.log('✅ JSON 解析修复成功！');
      console.log('响应状态:', uploadResponse.status);
      
    } catch (error) {
      if (error.response) {
        const status = error.response.status;
        const errorMessage = error.response.data?.error?.message || error.response.data?.message || '';
        
        console.log('响应状态:', status);
        console.log('错误信息:', errorMessage);
        
        // 分析错误类型
        if (errorMessage.includes('JSON') || errorMessage.includes('------WebK')) {
          console.log('❌ JSON 解析问题仍然存在');
          console.log('错误详情:', error.response.data);
          return false;
        } else if (status === 401) {
          console.log('✅ JSON 解析修复成功！（收到预期的认证错误）');
          return true;
        } else if (status === 400 && errorMessage.includes('token')) {
          console.log('✅ JSON 解析修复成功！（收到预期的 token 错误）');
          return true;
        } else {
          console.log('✅ JSON 解析修复成功！（收到其他业务逻辑错误）');
          console.log('这说明请求已经通过了 JSON 解析阶段');
          return true;
        }
      } else {
        console.log('❌ 网络错误:', error.message);
        return false;
      }
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

// 执行测试
if (require.main === module) {
  testFixVerification()
    .then((success) => {
      if (success) {
        console.log('\n🎉 修复验证成功！JSON 解析问题已解决');
        process.exit(0);
      } else {
        console.log('\n💥 修复验证失败！需要进一步调试');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 验证过程出错:', error.message);
      process.exit(1);
    });
}

module.exports = { testFixVerification };
